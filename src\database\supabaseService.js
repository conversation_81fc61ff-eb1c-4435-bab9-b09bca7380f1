/**
 * Supabase Service for GT Motorsports vehicle inventory
 */
import { supabase } from '../utils/supabase';

const BUCKET_NAME = 'car-images';
// Use a more reliable placeholder image source
const PLACEHOLDER_IMAGE = '/REDGTWHEEL.png';
const PROJECT_ID = import.meta.env.VITE_SUPABASE_URL ?
  import.meta.env.VITE_SUPABASE_URL.match(/https:\/\/([^.]+)\.supabase\.co/)?.[1] :
  null;

// --- Image Helper Functions (Keep as is) ---
const getPublicImageUrl = (path) => {
    console.log(`[getPublicImageUrl] Called with path: "${path}"`);
    if (!path) {
        console.log(`[getPublicImageUrl] No path provided, returning placeholder`);
        return PLACEHOLDER_IMAGE;
    }
    if (path.startsWith('http')) {
        console.log(`[getPublicImageUrl] Path is already a URL: "${path}"`);
        return path; // Handles existing URLs/placeholders
    }
    try {
        const { data } = supabase.storage.from(BUCKET_NAME).getPublicUrl(path);
        console.log(`[getPublicImageUrl] Supabase getPublicUrl result:`, data);
        
        if (data?.publicUrl) {
            console.log(`[getPublicImageUrl] Generated public URL: "${data.publicUrl}"`);
            return data.publicUrl;
        } else {
            console.error(`[getPublicImageUrl] Failed to generate public URL for path "${path}"`);
            return PLACEHOLDER_IMAGE;
        }
    } catch (error) {
        console.error(`[getPublicImageUrl] Error generating public URL for path "${path}":`, error);
        return PLACEHOLDER_IMAGE;
    }
};

// Cache for storing image URLs to prevent repeated fetching
const imageCache = new Map();

const getImagesFromStorageFolder = async (folderPath) => {
    // Check cache first
    if (imageCache.has(folderPath)) {
        console.log(`[Storage List] Using cached images for "${folderPath}"`);
        return imageCache.get(folderPath);
    }

    // console.log(`[Storage List] Called with folderPath: "${folderPath}"`); // Less verbose logging
    if (!folderPath) {
        const defaultImages = [PLACEHOLDER_IMAGE];
        imageCache.set(folderPath, defaultImages);
        return defaultImages;
    }

    try {
        const cleanPath = folderPath.replace(/^\/|\/$/g, '');
        console.log(`[Storage List] Attempting to list path: "${cleanPath}"`);

        const { data: files, error: listError } = await supabase.storage
          .from(BUCKET_NAME)
          .list(cleanPath, { limit: 100, offset: 0, sortBy: { column: 'name', order: 'asc' } });
        
        console.log(`[Storage List] List result for "${cleanPath}":`, {
            success: !listError,
            filesCount: files?.length || 0,
            error: listError ? listError.message : null
        });

        if (listError) {
          console.error(`[Storage List] Supabase error listing files in folder "${cleanPath}":`, listError.message || listError);
          const defaultImages = [PLACEHOLDER_IMAGE];
          imageCache.set(folderPath, defaultImages);
          return defaultImages;
        }
        // console.log(`[Storage List] Raw file list for "${cleanPath}":`, files?.length); // Less verbose logging

        if (!files || files.length === 0) {
            const defaultImages = [PLACEHOLDER_IMAGE];
            imageCache.set(folderPath, defaultImages);
            return defaultImages;
        }

        const imageFiles = files.filter(file =>
            file.name !== '.emptyFolderPlaceholder' &&
            /\.(jpg|jpeg|png|webp|gif)$/i.test(file.name)
        );
        // console.log(`[Storage List] Filtered to ${imageFiles.length} image files for "${cleanPath}"`); // Less verbose logging

        if (imageFiles.length === 0) {
            const defaultImages = [PLACEHOLDER_IMAGE];
            imageCache.set(folderPath, defaultImages);
            return defaultImages;
        }

        // Custom Numerical Sort
        imageFiles.sort((a, b) => {
            const numA = parseInt(a.name.split('.')[0], 10);
            const numB = parseInt(b.name.split('.')[0], 10);
            if (isNaN(numA) && isNaN(numB)) return a.name.localeCompare(b.name);
            if (isNaN(numA)) return 1;
            if (isNaN(numB)) return -1;
            return numA - numB;
        });
        // console.log(`[Storage List] Sorted image files for "${cleanPath}":`, imageFiles.map(f => f.name)); // Less verbose logging

        const sortedImageUrls = imageFiles.map(file => {
            const filePath = `${cleanPath}/${file.name}`;
            return getPublicImageUrl(filePath);
        }).filter(Boolean); // Filter out any nulls from getPublicImageUrl errors

        // If we got no valid URLs, return placeholder
        if (sortedImageUrls.length === 0) {
            const defaultImages = [PLACEHOLDER_IMAGE];
            imageCache.set(folderPath, defaultImages);
            return defaultImages;
        }

        // Store in cache
        imageCache.set(folderPath, sortedImageUrls);

        // console.log(`[Storage List] Found and sorted ${sortedImageUrls.length} image URLs for "${cleanPath}"`); // Less verbose logging
        return sortedImageUrls;

    } catch (error) {
        console.error(`[Storage List] Unexpected error fetching images from folder "${folderPath}":`, error);
        const defaultImages = [PLACEHOLDER_IMAGE];
        imageCache.set(folderPath, defaultImages);
        return defaultImages;
    }
};

// --- Main Data Fetching Functions ---

/**
 * Get vehicles from GTINV with pagination, processing images.
 */
export const getVehiclesFromSupabase = async (options = {}) => {
  const { page = 1, pageSize = 20, filters = {} } = options; // Default pageSize
  const startIndex = (page - 1) * pageSize;

  console.log(`[Supabase Service] Fetching vehicles: page ${page}, pageSize ${pageSize}, startIndex ${startIndex}`);
  try {
    // Increased timeout to 30 seconds to allow more time for complex queries
    const timeoutMs = 30000;
    const timeoutPromise = new Promise((_, reject) => {
      setTimeout(() => reject(new Error(`Supabase query timeout after ${timeoutMs}ms`)), timeoutMs);
    });
    console.log(`[Supabase Service] Query timeout set to ${timeoutMs}ms`);

    // Build query
    let query = supabase
      .from('GTINV')
      .select('*', { count: 'exact' }); // Ensure count is requested

    // Apply Filters from options - SIMPLIFIED APPROACH
    if (filters) {
      // Simple but effective search implementation
      if (filters.search && filters.search.trim()) {
        const searchTerm = filters.search.trim();
        console.log(`[Supabase Service] Searching for: "${searchTerm}"`);
        
        // Check if search term is numeric
        const isNumeric = /^\d+$/.test(searchTerm);
        
        // Create a list of conditions
        const conditions = [];
        
        // Always search these text fields
        conditions.push(`make.ilike.%${searchTerm}%`);
        conditions.push(`model.ilike.%${searchTerm}%`);
        conditions.push(`trim.ilike.%${searchTerm}%`);
        conditions.push(`stockNumber.ilike.%${searchTerm}%`);
        conditions.push(`vin.ilike.%${searchTerm}%`);
        
        // If numeric, add year search conditions
        if (isNumeric) {
          const numericValue = parseInt(searchTerm, 10);
          
          // Exact year match for 4 digits
          if (searchTerm.length === 4) {
            conditions.push(`year.eq.${numericValue}`);
          }
          // Partial year match for fewer digits
          else if (searchTerm.length < 4) {
            const lowerBound = numericValue * Math.pow(10, 4 - searchTerm.length);
            const upperBound = (numericValue + 1) * Math.pow(10, 4 - searchTerm.length);
            conditions.push(`and(year.gte.${lowerBound},year.lt.${upperBound})`);
          }
        }
        
        // Apply all conditions with OR
        query = query.or(conditions.join(','));
        
        console.log(`[Supabase Service] Search conditions: ${conditions.join(', ')}`);
      }

      // Range filters - FIXED
      // Price range filter - completely rewritten
      if (filters.priceRange && Array.isArray(filters.priceRange) && filters.priceRange.length === 2) {
        const minPrice = filters.priceRange[0];
        const maxPrice = filters.priceRange[1];
        const hasMinFilter = minPrice > 0;
        const hasMaxFilter = maxPrice < 300000;
        
        if (hasMinFilter || hasMaxFilter) {
          console.log(`[Supabase Service] Applying price range filter: min=${minPrice}, max=${maxPrice}`);
          
          // Create a filter function to handle the price range logic
          query = query.filter((builder) => {
            // Start with an empty filter
            let filter = builder;
            
            // For vehicles with non-null prices
            if (hasMinFilter && hasMaxFilter) {
              // Both min and max price filters
              filter = filter.or('and(price.gte.' + minPrice + ',price.lte.' + maxPrice + ')');
            } else if (hasMinFilter) {
              // Only min price filter
              filter = filter.or('price.gte.' + minPrice);
            } else if (hasMaxFilter) {
              // Only max price filter
              filter = filter.or('and(price.is.not.null,price.lte.' + maxPrice + ')');
            }
            
            // Include null prices only if no minimum price filter
            if (!hasMinFilter) {
              filter = filter.or('price.is.null');
            }
            
            return filter;
          });
        }
      }

      // Kilometers/mileage range filter - completely rewritten
      if (filters.kilometersRange && Array.isArray(filters.kilometersRange) && filters.kilometersRange.length === 2) {
        const minKm = filters.kilometersRange[0];
        const maxKm = filters.kilometersRange[1];
        const hasMinFilter = minKm > 0;
        const hasMaxFilter = maxKm < 500000;
        
        if (hasMinFilter || hasMaxFilter) {
          console.log(`[Supabase Service] Applying kilometers range filter: min=${minKm}, max=${maxKm}`);
          
          // Create a filter function to handle the mileage range logic
          query = query.filter((builder) => {
            // Start with an empty filter
            let filter = builder;
            
            // For vehicles with non-null mileage
            if (hasMinFilter && hasMaxFilter) {
              // Both min and max mileage filters
              filter = filter.or('and(mileage.gte.' + minKm + ',mileage.lte.' + maxKm + ')');
            } else if (hasMinFilter) {
              // Only min mileage filter
              filter = filter.or('mileage.gte.' + minKm);
            } else if (hasMaxFilter) {
              // Only max mileage filter
              filter = filter.or('and(mileage.is.not.null,mileage.lte.' + maxKm + ')');
            }
            
            // Include null mileage only if no minimum mileage filter
            if (!hasMinFilter) {
              filter = filter.or('mileage.is.null');
            }
            
            return filter;
          });
        }
      }

      // Year range filter - completely rewritten
      if (filters.yearRange && Array.isArray(filters.yearRange) && filters.yearRange.length === 2) {
        const currentYear = new Date().getFullYear() + 1;
        const minYear = filters.yearRange[0];
        const maxYear = filters.yearRange[1];
        const hasMinFilter = minYear > 1900;
        const hasMaxFilter = maxYear < currentYear;
        
        if (hasMinFilter || hasMaxFilter) {
          console.log(`[Supabase Service] Applying year range filter: min=${minYear}, max=${maxYear}`);
          
          // Create a filter function to handle the year range logic
          query = query.filter((builder) => {
            // Start with an empty filter
            let filter = builder;
            
            // For vehicles with non-null years
            if (hasMinFilter && hasMaxFilter) {
              // Both min and max year filters
              filter = filter.or('and(year.gte.' + minYear + ',year.lte.' + maxYear + ')');
            } else if (hasMinFilter) {
              // Only min year filter
              filter = filter.or('year.gte.' + minYear);
            } else if (hasMaxFilter) {
              // Only max year filter
              filter = filter.or('and(year.is.not.null,year.lte.' + maxYear + ')');
            }
            
            // Include null years only if no minimum year filter
            if (!hasMinFilter) {
              filter = filter.or('year.is.null');
            }
            
            return filter;
          });
        }
      }

      // Array filters - SIMPLIFIED
      // Make filter
      if (filters.make && Array.isArray(filters.make) && filters.make.length > 0) {
        const includeNull = filters.make.includes('Unknown');
        const otherMakes = filters.make.filter(m => m !== 'Unknown');

        if (otherMakes.length > 0 && includeNull) {
          // Both specific makes and nulls
          query = query.or(
            `make.in.(${otherMakes.join(',')})`,
            `make.is.null`
          );
        } else if (otherMakes.length > 0) {
          // Only specific makes
          query = query.in('make', otherMakes);
        } else if (includeNull) {
          // Only nulls
          query = query.is('make', null);
        }
      }

      // Model filter
      if (filters.model && Array.isArray(filters.model) && filters.model.length > 0) {
        const includeNull = filters.model.includes('Unknown');
        const otherModels = filters.model.filter(m => m !== 'Unknown');

        if (otherModels.length > 0 && includeNull) {
          // Both specific models and nulls
          query = query.or(
            `model.in.(${otherModels.join(',')})`,
            `model.is.null`
          );
        } else if (otherModels.length > 0) {
          // Only specific models
          query = query.in('model', otherModels);
        } else if (includeNull) {
          // Only nulls
          query = query.is('model', null);
        }
      }

      // Body type filter - with improved logging
      if (filters.bodyType && Array.isArray(filters.bodyType) && filters.bodyType.length > 0) {
        const includeNull = filters.bodyType.includes('Unknown');
        const otherTypes = filters.bodyType.filter(t => t !== 'Unknown');
        
        console.log(`[Supabase Service] Applying bodyType filter: ${JSON.stringify(filters.bodyType)}`);
        console.log(`[Supabase Service] includeNull: ${includeNull}, otherTypes: ${JSON.stringify(otherTypes)}`);

        if (otherTypes.length > 0 && includeNull) {
          // Both specific types and nulls
          console.log(`[Supabase Service] Applying bodyStyle filter with both specific types and nulls`);
          query = query.or(
            `bodyStyle.in.(${otherTypes.join(',')})`,
            `bodyStyle.is.null`
          );
        } else if (otherTypes.length > 0) {
          // Only specific types
          console.log(`[Supabase Service] Applying bodyStyle filter with only specific types: ${otherTypes.join(',')}`);
          query = query.in('bodyStyle', otherTypes);
        } else if (includeNull) {
          // Only nulls
          console.log(`[Supabase Service] Applying bodyStyle filter for null values only`);
          query = query.is('bodyStyle', null);
        }
      }

      // Transmission filter
      if (filters.transmission && Array.isArray(filters.transmission) && filters.transmission.length > 0) {
        const includeNull = filters.transmission.includes('Unknown');
        const otherTransmissions = filters.transmission.filter(t => t !== 'Unknown');

        if (otherTransmissions.length > 0 && includeNull) {
          // Both specific transmissions and nulls
          query = query.or(
            `transmission.in.(${otherTransmissions.join(',')})`,
            `transmission.is.null`
          );
        } else if (otherTransmissions.length > 0) {
          // Only specific transmissions
          query = query.in('transmission', otherTransmissions);
        } else if (includeNull) {
          // Only nulls
          query = query.is('transmission', null);
        }
      }

      // Fuel type filter
      if (filters.fuelType && Array.isArray(filters.fuelType) && filters.fuelType.length > 0) {
        const includeNull = filters.fuelType.includes('Unknown');
        const otherFuelTypes = filters.fuelType.filter(f => f !== 'Unknown');

        if (otherFuelTypes.length > 0 && includeNull) {
          // Both specific fuel types and nulls
          query = query.or(
            `fuelType.in.(${otherFuelTypes.join(',')})`,
            `fuelType.is.null`
          );
        } else if (otherFuelTypes.length > 0) {
          // Only specific fuel types
          query = query.in('fuelType', otherFuelTypes);
        } else if (includeNull) {
          // Only nulls
          query = query.is('fuelType', null);
        }
      }

      // Color filter
      if (filters.color && Array.isArray(filters.color) && filters.color.length > 0) {
        const includeNull = filters.color.includes('Unknown');
        const otherColors = filters.color.filter(c => c !== 'Unknown');

        if (otherColors.length > 0 && includeNull) {
          // Both specific colors and nulls
          query = query.or(
            `exteriorColor.in.(${otherColors.join(',')})`,
            `exteriorColor.is.null`
          );
        } else if (otherColors.length > 0) {
          // Only specific colors
          query = query.in('exteriorColor', otherColors);
        } else if (includeNull) {
          // Only nulls
          query = query.is('exteriorColor', null);
        }
      }
    }

    // Apply pagination and ordering
    const endIndex = startIndex + pageSize - 1;
    console.log(`[Supabase Service] Applying range: ${startIndex} to ${endIndex} (inclusive)`);

    // Apply sorting based on options
    if (filters && filters.sortOption) {
      switch (filters.sortOption) {
        case 'newest':
          query = query.order('year', { ascending: false, nullsLast: true })
                       .order('id', { ascending: true });
          break;
        case 'oldest':
          query = query.order('year', { ascending: true, nullsFirst: true })
                       .order('id', { ascending: true });
          break;
        case 'price-high':
          // Ensure null prices come last when sorting high to low
          query = query.order('price', { ascending: false, nullsLast: true })
                       .order('id', { ascending: true });
          break;
        case 'price-low':
          query = query.order('price', { ascending: true, nullsLast: true })
                       .order('id', { ascending: true });
          break;
        case 'mileage-low':
          query = query.order('mileage', { ascending: true, nullsLast: true })
                       .order('id', { ascending: true });
          break;
        case 'mileage-high':
          query = query.order('mileage', { ascending: false, nullsLast: true })
                       .order('id', { ascending: true });
          break;
        default:
          // Default sort by newest
          query = query.order('year', { ascending: false })
                       .order('id', { ascending: true });
      }
    } else {
      // Default sort by newest
      query = query.order('year', { ascending: false })
                   .order('id', { ascending: true });
    }

    // Apply pagination
    query = query.range(startIndex, endIndex);

    // Execute query with timeout
    console.log(`[Supabase Service] Executing query for page ${page}...`);

    // Log the query structure for debugging
    console.log('[Supabase Service] Query structure:', JSON.stringify({
      table: 'GTINV',
      page,
      pageSize,
      startIndex,
      filters: Object.keys(filters).reduce((acc, key) => {
        // Don't log the actual values for brevity, just indicate if they're present
        acc[key] = filters[key] ? (Array.isArray(filters[key]) ? `Array[${filters[key].length}]` : 'Present') : 'Not present';
        return acc;
      }, {})
    }, null, 2));

    const queryPromise = query;
    let data, error, count;

    try {
      const result = await Promise.race([queryPromise, timeoutPromise]);
      data = result.data;
      error = result.error;
      count = result.count;

      // Detailed logging of the result
      console.log(`[Supabase Service] Query execution complete for page ${page}.`);
      console.log(`[Supabase Service] Result status: ${error ? 'Error' : 'Success'}`);
      console.log(`[Supabase Service] Data received: ${data ? 'Yes' : 'No'}, Count: ${count !== undefined ? count : 'Not provided'}`);

      if (error) {
        console.error('[Supabase Service] Error details:', error);
        throw error; // Re-throw to be caught by the store
      }

      if (!data) {
        console.warn('[Supabase Service] No data returned but no error either.');
      }

    } catch (queryError) {
      console.error('[Supabase Service] Exception during query execution:', queryError);

      // Check if it's a timeout error
      if (queryError.message && queryError.message.includes('timeout')) {
        console.error('[Supabase Service] Query timed out. Consider simplifying filters or increasing timeout.');
      }
      
      // Check for 416 Range Not Satisfiable error
      if (queryError.status === 416 ||
          (queryError.message && queryError.message.includes('416')) ||
          (queryError.error && queryError.error.includes('Requested range not satisfiable'))) {
        console.error('[Supabase Service] 416 Range Not Satisfiable error. This typically means the requested range (offset) exceeds the available data for the current filters.');
        
        // Enhance the error with more information
        const enhancedError = new Error('Requested range not satisfiable');
        enhancedError.code = '416';
        enhancedError.originalError = queryError;
        enhancedError.details = {
          page: page,
          pageSize: pageSize,
          startIndex: startIndex,
          filters: filters
        };
        
        throw enhancedError;
      }

      throw queryError; // Re-throw to be caught by the store
    }

    // Detailed logging for count and data
    console.log(`[Supabase Service] Query successful for page ${page}. Received ${data?.length || 0} records. Total count reported: ${count}`);

    if (count === undefined || count === null) {
      console.warn('[Supabase Service] Warning: Supabase did not return a total count. This may affect pagination.');
    } else if (count === 0) {
      console.warn('[Supabase Service] Warning: Total count is zero. This suggests no matching records in the database.');
    } else if (data && data.length === 0 && count > 0) {
      console.warn('[Supabase Service] Warning: Count is positive but no data returned. This may indicate a pagination issue.');
    }


    if (!data) {
        console.warn('[Supabase Service] No vehicle data returned from GTINV for this page.');
        return { vehicles: [], totalCount: count || 0 }; // Return count even if data is empty
    }

    // Process items (transformation and image fetching)
    const transformedData = await Promise.all(data.map(async (item) => {
        // Log raw vehicle data to check if image_path is present
        console.log(`[Supabase Service] Raw vehicle data for ID ${item.id}:`, {
            id: item.id,
            image_path: item.image_path,
            hasImagePath: !!item.image_path
        });
        
        let primaryImage = PLACEHOLDER_IMAGE;
        let gallery = [];
        try {
            if (item.image_path) {
                // Use a longer timeout (30 seconds) and ensure we always get a valid result
                const sortedFolderImages = await getImagesFromStorageFolder(item.image_path);
                
                // Log the results of getImagesFromStorageFolder
                console.log(`[Supabase Service] Images found for vehicle ID ${item.id} in path ${item.image_path}:`, {
                    count: sortedFolderImages.length,
                    firstImage: sortedFolderImages[0],
                    isPlaceholder: sortedFolderImages[0] === PLACEHOLDER_IMAGE,
                    allImages: sortedFolderImages
                });

                // We've improved getImagesFromStorageFolder to always return at least [PLACEHOLDER_IMAGE]
                // so this should always have at least one image
                gallery = sortedFolderImages;
                const primaryIndex = gallery.findIndex(url => /\/1\.(jpg|jpeg|png|webp|gif)$/i.test(url));
                primaryImage = (primaryIndex !== -1) ? gallery[primaryIndex] : gallery[0];
                
                // Log the primary image selection
                console.log(`[Supabase Service] Primary image for vehicle ID ${item.id}:`, {
                    primaryIndex,
                    primaryImage,
                    isPlaceholder: primaryImage === PLACEHOLDER_IMAGE,
                    regex: '/1.(jpg|jpeg|png|webp|gif)$'
                });
            }
            // Double-check to ensure we always have at least one image
            if (gallery.length === 0) gallery = [primaryImage]; // Ensure gallery has at least primary
        } catch (itemError) {
            console.error(`[Image Logic] Error processing images for item ID ${item.id}:`, itemError);
            primaryImage = PLACEHOLDER_IMAGE;
            gallery = [PLACEHOLDER_IMAGE];
        }

        // Transform data (Keep your existing robust transformation)
        try {
            // Enhanced normalization for price and mileage values for proper sorting
            let normalizedPrice = null;
            if (item.price !== undefined && item.price !== null) {
                // Convert to number and ensure it's valid
                let priceStr = String(item.price).replace(/[^\d.-]/g, '');
                // Handle empty string case
                if (priceStr === '' || priceStr === '.') priceStr = '0';
                const priceNum = Number(priceStr);
                normalizedPrice = !isNaN(priceNum) ? priceNum : 0;
                
                // Log any suspicious price values for debugging
                if (normalizedPrice > 0 && normalizedPrice < 1000) {
                    console.warn(`[Supabase Service] Suspiciously low price for vehicle ID ${item.id}: ${normalizedPrice} (original: ${item.price})`);
                } else if (normalizedPrice > 200000) {
                    console.warn(`[Supabase Service] Suspiciously high price for vehicle ID ${item.id}: ${normalizedPrice} (original: ${item.price})`);
                }
            }
            
            let normalizedMileage = null;
            if (item.mileage !== undefined && item.mileage !== null) {
                // Convert to number and ensure it's valid
                let mileageStr = String(item.mileage).replace(/[^\d.-]/g, '');
                // Handle empty string case
                if (mileageStr === '' || mileageStr === '.') mileageStr = '0';
                const mileageNum = Number(mileageStr);
                normalizedMileage = !isNaN(mileageNum) ? mileageNum : 0;
                
                // Log any suspicious mileage values for debugging
                if (normalizedMileage > 0 && normalizedMileage < 100) {
                    console.warn(`[Supabase Service] Suspiciously low mileage for vehicle ID ${item.id}: ${normalizedMileage} (original: ${item.mileage})`);
                } else if (normalizedMileage > 500000) {
                    console.warn(`[Supabase Service] Suspiciously high mileage for vehicle ID ${item.id}: ${normalizedMileage} (original: ${item.mileage})`);
                }
            }
            
            // Log any unusual values for debugging
            if ((normalizedMileage < 500 && normalizedMileage > 0) || normalizedMileage > 500000) {
                console.log(`[Supabase Service] Unusual mileage value for vehicle ID ${item.id}: ${normalizedMileage} (original: ${item.mileage})`);
            }
            
            if (normalizedPrice > 200000 || (normalizedPrice < 1000 && normalizedPrice > 0)) {
                console.log(`[Supabase Service] Unusual price value for vehicle ID ${item.id}: ${normalizedPrice} (original: ${item.price})`);
            }
            
            const title = `${item.year || ''} ${item.make || ''} ${item.model || ''} ${item.trim || ''}`.replace(/\s+/g, ' ').trim();
            return {
                id: item.id,
                title: title || 'Vehicle',
                price: normalizedPrice,
                specialPrice: item.specialPrice || null,
                image: primaryImage,
                gallery: gallery,
                mileage: normalizedMileage,
                year: item.year || null,
                make: item.make || '',
                model: item.model || '',
                trim: item.trim || '',
                doors: item.doors || 0,
                bodyStyle: item.bodyStyle || '',
                engine: item.engine || '',
                engineSize: item.engineSize || '',
                drivetrain: item.driveTrain || item.drivetrain || '',
                transmission: item.transmission || '',
                exteriorColor: item.exteriorColor || '',
                interiorColor: item.interiorColor || '',
                passengers: item.passengers || 0,
                fuelType: item.fuelType || '',
                cityFuel: item.cityFuelEconomy || item.cityFuel || '',
                hwyFuel: item.highwayFuelEconomy || item.hwyFuel || '',
                stockNumber: item.stockNumber || `GT-${item.id || Math.floor(Math.random() * 10000)}`,
                vin: item.vin || '',
                carfaxLink: item.carfaxLink || '',
                highlights: typeof item.highlights === 'string' && item.highlights.trim()
                            ? item.highlights.split(/[,;|\n]+/).map(h => h.trim()).filter(Boolean)
                            : (Array.isArray(item.highlights) ? item.highlights : []),
                features: typeof item.features === 'string' && item.features.trim()
                          ? item.features.split(/[,;|\n]+/).map(f => f.trim()).filter(Boolean)
                          : (Array.isArray(item.features) ? item.features : []),
                description: item.description || ''
            };
            
            // Log the final transformed vehicle data
            console.log(`[Supabase Service] Transformed vehicle data for ID ${item.id}:`, {
                id: item.id,
                title: title || 'Vehicle',
                image: primaryImage,
                hasImage: !!primaryImage,
                isPlaceholder: primaryImage === PLACEHOLDER_IMAGE,
                galleryLength: gallery.length
            });
        } catch (transformError) {
             console.error(`[Transform Error] Error transforming vehicle data for ID ${item.id}:`, transformError);
             return { // Minimal fallback
                id: item.id || Date.now(), title: 'Vehicle Data Error', price: 0, image: PLACEHOLDER_IMAGE, gallery: [PLACEHOLDER_IMAGE],
             };
        }
    }));

    console.log(`[Supabase Service] Loaded and transformed ${transformedData.length} vehicles for page ${page}.`);
    // console.log(`[Supabase Service] Vehicle IDs in this page:`, transformedData.map(v => v.id)); // Optional detailed log

    return {
      vehicles: transformedData,
      totalCount: count || 0 // Return the reported total count
    };
  } catch (error) {
    console.error('[Supabase Service] Error in getVehiclesFromSupabase:', error);
    // Return empty state on error? Or re-throw? Re-throwing is better for store handling.
    throw error;
  }
};

/**
 * Get a single vehicle by ID.
 */
export const getVehicleByIdFromSupabase = async (id) => {
  console.log(`[Supabase Service] Fetching vehicle ID ${id}...`);
  if (!id) {
      console.error('[Supabase Service] getVehicleByIdFromSupabase called with invalid ID.');
      return null;
  }
  const numericId = typeof id === 'string' ? parseInt(id, 10) : id;
  if (isNaN(numericId)) {
      console.error(`[Supabase Service] Invalid numeric ID after parsing: ${id}`);
      return null;
  }

  try {
    let { data, error } = await supabase
      .from('GTINV')
      .select('*')
      .eq('id', numericId)
      .maybeSingle();

    if (error) throw error; // Let the caller handle it

    if (!data) {
      console.log(`[Supabase Service] Vehicle with ID ${numericId} not found.`);
      return null;
    }

    // --- Image Handling Logic (Same as above) ---
    let primaryImage = PLACEHOLDER_IMAGE;
    let gallery = [];
    try {
        if (data.image_path) {
            // Use the improved getImagesFromStorageFolder that always returns at least [PLACEHOLDER_IMAGE]
            const sortedFolderImages = await getImagesFromStorageFolder(data.image_path);

            // This should always have at least one image now
            gallery = sortedFolderImages;
            const primaryIndex = gallery.findIndex(url => /\/1\.(jpg|jpeg|png|webp|gif)$/i.test(url));
            primaryImage = (primaryIndex !== -1) ? gallery[primaryIndex] : gallery[0];
        }
        // Double-check to ensure we always have at least one image
        if (gallery.length === 0) gallery = [primaryImage];
    } catch (imageError) {
        console.error(`[Image Logic] Error processing images for item ID ${data.id}:`, imageError);
        primaryImage = PLACEHOLDER_IMAGE;
        gallery = [PLACEHOLDER_IMAGE];
    }
    // --- End Image Handling ---

    // --- Transformation with enhanced normalized values ---
    // Enhanced normalization for price and mileage values for proper sorting
    let normalizedPrice = null;
    if (data.price !== undefined && data.price !== null) {
        // Convert to number and ensure it's valid
        let priceStr = String(data.price).replace(/[^\d.-]/g, '');
        // Handle empty string case
        if (priceStr === '' || priceStr === '.') priceStr = '0';
        const priceNum = Number(priceStr);
        normalizedPrice = !isNaN(priceNum) ? priceNum : 0;
        
        // Log any suspicious price values for debugging
        if (normalizedPrice > 0 && normalizedPrice < 1000) {
            console.warn(`[Supabase Service] Suspiciously low price for vehicle ID ${data.id}: ${normalizedPrice} (original: ${data.price})`);
        } else if (normalizedPrice > 200000) {
            console.warn(`[Supabase Service] Suspiciously high price for vehicle ID ${data.id}: ${normalizedPrice} (original: ${data.price})`);
        }
    }
    
    let normalizedMileage = null;
    if (data.mileage !== undefined && data.mileage !== null) {
        // Convert to number and ensure it's valid
        let mileageStr = String(data.mileage).replace(/[^\d.-]/g, '');
        // Handle empty string case
        if (mileageStr === '' || mileageStr === '.') mileageStr = '0';
        const mileageNum = Number(mileageStr);
        normalizedMileage = !isNaN(mileageNum) ? mileageNum : 0;
        
        // Log any suspicious mileage values for debugging
        if (normalizedMileage > 0 && normalizedMileage < 100) {
            console.warn(`[Supabase Service] Suspiciously low mileage for vehicle ID ${data.id}: ${normalizedMileage} (original: ${data.mileage})`);
        } else if (normalizedMileage > 500000) {
            console.warn(`[Supabase Service] Suspiciously high mileage for vehicle ID ${data.id}: ${normalizedMileage} (original: ${data.mileage})`);
        }
    }
    
    const title = `${data.year || ''} ${data.make || ''} ${data.model || ''} ${data.trim || ''}`.replace(/\s+/g, ' ').trim();
    const transformedData = {
        id: data.id, title: title || 'Vehicle', price: normalizedPrice, specialPrice: data.specialPrice || null,
        image: primaryImage, gallery: gallery, mileage: normalizedMileage, year: data.year || null, make: data.make || '',
        model: data.model || '', trim: data.trim || '', doors: data.doors || 0, bodyStyle: data.bodyStyle || '',
        engine: data.engine || '', engineSize: data.engineSize || '', drivetrain: data.driveTrain || data.drivetrain || '',
        transmission: data.transmission || '', exteriorColor: data.exteriorColor || '', interiorColor: data.interiorColor || '',
        passengers: data.passengers || 0, fuelType: data.fuelType || '', cityFuel: data.cityFuelEconomy || data.cityFuel || '',
        hwyFuel: data.highwayFuelEconomy || data.hwyFuel || '', stockNumber: data.stockNumber || `GT-${data.id || Date.now()}`,
        vin: data.vin || '', carfaxLink: data.carfaxLink || '',
        highlights: typeof data.highlights === 'string' && data.highlights.trim() ? data.highlights.split(/[,;|\n]+/).map(h => h.trim()).filter(Boolean) : (Array.isArray(data.highlights) ? data.highlights : []),
        features: typeof data.features === 'string' && data.features.trim() ? data.features.split(/[,;|\n]+/).map(f => f.trim()).filter(Boolean) : (Array.isArray(data.features) ? data.features : []),
        description: data.description || ''
    };
    // --- End Transformation ---

    console.log(`[Supabase Service] Successfully loaded vehicle ID ${id}.`);
    return transformedData;

  } catch (error) {
    console.error(`[Supabase Service] Error in getVehicleByIdFromSupabase for ID ${id}:`, error);
    throw error; // Re-throw
  }
};


// --- CUD Operations (Using GTINV table directly) ---
export const addVehicleToSupabase = async (vehicleData) => {
  console.log('=== addVehicleToSupabase FUNCTION CALLED ===');
  console.log('vehicleData received:', vehicleData);
  try {
    console.log('addVehicleToSupabase called with data:', vehicleData);
    console.log('Using GTINV table directly (no vehicles table exists)');
    return addVehicleToGTINV(vehicleData);
  } catch (error) {
    console.error('Error in addVehicleToSupabase:', error);
    throw error;
  }
};

export const updateVehicleInSupabase = async (id, vehicleData) => {
  try {
    console.log('Updating vehicle in GTINV table');
    return updateVehicleInGTINV(id, vehicleData);
  } catch (error) {
    console.error(`Error in updateVehicleInSupabase:`, error);
    throw error;
  }
};

export const deleteVehicleFromSupabase = async (id) => {
  try {
    console.log('Deleting vehicle from GTINV table');
    return deleteVehicleFromGTINV(id);
  } catch (error) {
    console.error(`Error in deleteVehicleFromSupabase:`, error);
    throw error;
  }
};

export const initSupabaseDatabase = async () => {
  try {
    console.log('Checking GTINV table...');

    const { error: gtinvError } = await supabase
      .from('GTINV')
      .select('id')
      .limit(1);

    if (gtinvError && gtinvError.code === '42P01') {
      console.log('GTINV table not found, creating it');

      // Create the GTINV table
      const { error: createError } = await supabase.rpc('create_gtinv_table');

      if (createError) {
        console.error('Error creating GTINV table:', createError);
        console.log('Table creation via RPC failed, this is expected in development. Tables should be created manually in the Supabase dashboard.');
      }
    } else {
      console.log('GTINV table exists');
    }

    return true;
  } catch (error) {
    console.error('Error initializing Supabase database:', error);
    return false;
  }
};

// Helper functions for GTINV operations
const addVehicleToGTINV = async (vehicleData) => {
  try {
    // Extract all fields that exist in the GTINV table
    const {
      title, year, make, model, trim, price,
      image, mileage, bodyStyle, engine, drivetrain,
      exteriorColor, interiorColor
    } = vehicleData;

    // Extract image path from the image URL if it exists
    let image_path = null;
    if (image) {
      if (image.includes('supabase.co/storage/v1/object/public/car-images/')) {
        const match = image.match(/car-images\/(.+)$/);
        if (match && match[1]) {
          image_path = match[1];
        }
      } else if (!image.startsWith('http')) {
        image_path = image;
      }
    }

    // Get tenant_id from current user session
    const { data: { user } } = await supabase.auth.getUser();
    const tenant_id = user?.user_metadata?.tenant_id || user?.id;

    // Create the vehicle object with correct column names
    const gtinvVehicle = {
      title: title || `${year} ${make} ${model} ${trim || ''}`.trim(),
      year: year || new Date().getFullYear(),
      make: make || '',
      model: model || '',
      trim: trim || '',
      price: price || 0,
      image_path,
      mileage: mileage || 0,
      bodyStyle: bodyStyle || '',
      engine: engine || '',
      driveTrain: drivetrain || '',
      exteriorColor: exteriorColor || '',
      interiorColor: interiorColor || '',
      tenant_id: tenant_id // Add tenant_id for RLS
    };

    console.log('Inserting vehicle to GTINV:', gtinvVehicle);

    const { data, error } = await supabase
      .from('GTINV')
      .insert([gtinvVehicle])
      .select();

    if (error) {
      console.error('Error inserting vehicle to GTINV:', error);
      throw error;
    }

    if (!data || data.length === 0) {
      throw new Error('Failed to add vehicle: No data returned');
    }

    console.log('Vehicle added to GTINV successfully:', data[0]);
    return data[0].id;
  } catch (error) {
    console.error('Error in addVehicleToGTINV:', error);
    throw error;
  }
};

const updateVehicleInGTINV = async (id, vehicleData) => {
  try {
    // Extract all fields that exist in the GTINV table
    const {
      year, make, model, trim, price,
      bodyStyle, engine, drivetrain, exteriorColor, interiorColor, mileage,
      title
    } = vehicleData;

    // Create an update object with all fields that exist in the GTINV table
    const updateData = {};

    if (title !== undefined) updateData.title = title;
    if (year !== undefined) updateData.year = year;
    if (make !== undefined) updateData.make = make;
    if (model !== undefined) updateData.model = model;
    if (trim !== undefined) updateData.trim = trim;
    if (price !== undefined) updateData.price = price;
    if (mileage !== undefined) updateData.mileage = mileage;
    if (bodyStyle !== undefined) updateData.bodyStyle = bodyStyle;
    if (engine !== undefined) updateData.engine = engine;
    if (drivetrain !== undefined) updateData.driveTrain = drivetrain;
    if (exteriorColor !== undefined) updateData.exteriorColor = exteriorColor;
    if (interiorColor !== undefined) updateData.interiorColor = interiorColor;

    // Only update if there are fields to update
    if (Object.keys(updateData).length === 0) {
      console.log('No fields to update in GTINV table');
      return id;
    }

    console.log('Updating vehicle in GTINV:', updateData);

    const { error } = await supabase
      .from('GTINV')
      .update(updateData)
      .eq('id', id);

    if (error) {
      console.error('Error updating vehicle in GTINV:', error);
      throw error;
    }

    console.log('Vehicle updated in GTINV successfully');
    return id;
  } catch (error) {
    console.error('Error in updateVehicleInGTINV:', error);
    throw error;
  }
};

const deleteVehicleFromGTINV = async (id) => {
  try {
    console.log(`Deleting vehicle with ID ${id} from GTINV table`);

    const { error } = await supabase
      .from('GTINV')
      .delete()
      .eq('id', id);

    if (error) {
      console.error(`Error deleting vehicle with ID ${id} from GTINV:`, error);
      throw error;
    }

    console.log(`Vehicle with ID ${id} deleted from GTINV successfully`);
    return true;
  } catch (error) {
    console.error(`Error in deleteVehicleFromGTINV:`, error);
    throw error;
  }
};

// Restore the non-paginated function for backward compatibility
export const getAllVehiclesFromSupabase = async () => {
  console.log('[Supabase Service] getAllVehiclesFromSupabase called (non-paginated)');
  try {
    // Use the paginated function but request a large page size to get all vehicles
    const { vehicles } = await getVehiclesFromSupabase({
      page: 1,
      pageSize: 1000 // Large enough to get all vehicles in most cases
    });

    console.log(`[Supabase Service] Fetched all ${vehicles?.length || 0} vehicles (non-paginated)`);
    return vehicles || [];
  } catch (error) {
    console.error('[Supabase Service] Error in getAllVehiclesFromSupabase:', error);
    throw error;
  }
};

// Export default object
export default {
  getAllVehiclesFromSupabase, // Restore this for backward compatibility
  getVehiclesFromSupabase, // The paginated version
  getVehicleByIdFromSupabase,
  addVehicleToSupabase,
  updateVehicleInSupabase,
  deleteVehicleFromSupabase,
  initSupabaseDatabase
};