/**
 * Supabase Service for GT Motorsports vehicle inventory
 * This file provides functions to interact with the Supabase database
 */
import { supabase } from '../utils/supabase';

/**
 * Get all vehicles from the GTINV table
 * @returns {Promise} Promise that resolves with an array of vehicles
 */
export const getAllVehiclesFromSupabase = async () => {
  try {
    console.log('Fetching vehicles from GTINV table');
    return getAllVehiclesFromGTINV();
  } catch (error) {
    console.error('Error in getAllVehiclesFromSupabase:', error);
    throw error;
  }
};

// New functions for body style filtering
export const getVehiclesByBodyStyle = async (bodyStyle) => {
  try {
    console.log('Fetching vehicles by body style from GTINV table');
    return getVehiclesByBodyStyleFromGTINV(bodyStyle);
  } catch (error) {
    console.error('Error in getVehiclesByBodyStyle:', error);
    throw error;
  }
};

const getVehiclesByBodyStyleFromGTINV = async (bodyStyle) => {
  try {
    const { data, error } = await supabase
      .from('GTINV')
      .select('*')
      .eq('bodyStyle', bodyStyle);

    if (error) throw error;

    return data.map(item => ({
      id: item.id,
      title: `${item.year} ${item.make} ${item.model} ${item.trim || ''}`.trim(),
      price: item.price || 0,
      image: item.image_path ? 
        `https://wjqlfcxgrdfyqpjsbnyp.supabase.co/storage/v1/object/public/car-images/${item.image_path}` : 
        'https://via.placeholder.com/400x300?text=No+Image',
      bodyStyle: item.bodyStyle || ''
    }));
  } catch (error) {
    console.error('Error fetching from GTINV by body style:', error);
    throw error;
  }
};

/** 
 * Fallback function to get vehicles from the GTINV table
 * @returns {Promise} Promise that resolves with an array of vehicles
 */
const getAllVehiclesFromGTINV = async () => {
  try {
    const { data, error } = await supabase.from('GTINV').select('*');
    
    if (error) {
      console.error('Error fetching vehicles from GTINV table:', error);
      throw error;
    }
    
    // Transform the data to match the expected format in the application
    const transformedData = data.map(item => {
      // Get the image URL from image_path if it exists
      const getImageUrl = (imagePath) => {
        if (!imagePath) return 'https://via.placeholder.com/400x300?text=No+Image';
        
        // If it's already a full URL, return it
        if (imagePath.startsWith('http')) return imagePath;
        
        // Construct the full URL using the storage bucket
        const projectId = 'wjqlfcxgrdfyqpjsbnyp';
        return `https://${projectId}.supabase.co/storage/v1/object/public/car-images/${imagePath}`;
      };

      // Generate a title from the year, make, and model
      const title = `${item.year} ${item.make} ${item.model} ${item.trim || ''}`.trim();
      
      // Get the image URL from image_path
      const imageUrl = getImageUrl(item.image_path);
      
      return {
        id: item.id,
        title: title,
        price: item.price || 0,
        specialPrice: null,
        image: imageUrl,
        gallery: [imageUrl], // Use the same image for gallery if no others exist
        mileage: item.mileage || 0,
        year: item.year || new Date().getFullYear(),
        make: item.make || '',
        model: item.model || '',
        trim: item.trim || '',
        doors: 0,
        bodyStyle: item.bodyStyle || '',
        engine: item.engine || '',
        engineSize: '',
        drivetrain: item.driveTrain || '',
        transmission: '',
        exteriorColor: item.exteriorColor || '',
        interiorColor: item.interiorColor || '',
        passengers: 0,
        fuelType: '',
        cityFuel: '',
        hwyFuel: '',
        stockNumber: item.stockNumber || `GT-${Math.floor(Math.random() * 10000)}`,
        vin: '',
        highlights: [],
        features: [],
        description: ''
      };
    });
    
    console.log(`Loaded ${transformedData.length} vehicles from GTINV table`);
    return transformedData;
  } catch (error) {
    console.error('Error in getAllVehiclesFromGTINV:', error);
    throw error;
  }
};

/**
 * Get a vehicle by ID
 * @param {number} id - The vehicle ID
 * @returns {Promise} Promise that resolves with the vehicle data
 */
export const getVehicleByIdFromSupabase = async (id) => {
  try {
    console.log('Fetching vehicle by ID from GTINV table');
    return getVehicleByIdFromGTINV(id);
  } catch (error) {
    console.error(`Error in getVehicleByIdFromSupabase:`, error);
    throw error;
  }
};

/**
 * Fallback function to get a vehicle by ID from the GTINV table
 * @param {number} id - The vehicle ID
 * @returns {Promise} Promise that resolves with the vehicle data
 */
const getVehicleByIdFromGTINV = async (id) => {
  try {
    const { data, error } = await supabase
      .from('GTINV')
      .select('*')
      .eq('id', id)
      .single();
    
    if (error) {
      console.error(`Error fetching vehicle with ID ${id} from GTINV table:`, error);
      throw error;
    }
    
    if (!data) {
      return null;
    }
    
    // Get the image URL from image_path if it exists
    const getImageUrl = (imagePath) => {
      if (!imagePath) return 'https://via.placeholder.com/400x300?text=No+Image';
      
      // If it's already a full URL, return it
      if (imagePath.startsWith('http')) return imagePath;
      
      // Construct the full URL using the storage bucket
      const projectId = 'wjqlfcxgrdfyqpjsbnyp';
      return `https://${projectId}.supabase.co/storage/v1/object/public/car-images/${imagePath}`;
    };

    // Generate a title from the year, make, and model
    const title = `${data.year} ${data.make} ${data.model} ${data.trim || ''}`.trim();
    
    // Get the image URL from image_path
    const imageUrl = getImageUrl(data.image_path);
    
    // Transform the data to match the expected format in the application
    const transformedData = {
      id: data.id,
      title: title,
      price: data.price || 0,
      specialPrice: null,
      image: imageUrl,
      gallery: [imageUrl], // Use the same image for gallery if no others exist
      mileage: data.mileage || 0,
      year: data.year || new Date().getFullYear(),
      make: data.make || '',
      model: data.model || '',
      trim: data.trim || '',
      doors: 0,
      bodyStyle: data.bodyStyle || '',
      engine: data.engine || '',
      engineSize: '',
      drivetrain: data.driveTrain || '',
      transmission: '',
      exteriorColor: data.exteriorColor || '',
      interiorColor: data.interiorColor || '',
      passengers: 0,
      fuelType: '',
      cityFuel: '',
      hwyFuel: '',
      stockNumber: data.stockNumber || `GT-${Math.floor(Math.random() * 10000)}`,
      vin: '',
      highlights: [],
      features: [],
      description: ''
    };
    
    return transformedData;
  } catch (error) {
    console.error(`Error in getVehicleByIdFromGTINV:`, error);
    throw error;
  }
};

/**
 * Add a vehicle to the database
 * @param {Object} vehicleData - The vehicle data to add
 * @returns {Promise} Promise that resolves with the new vehicle ID
 */
export const addVehicleToSupabase = async (vehicleData) => {
  console.log('=== addVehicleToSupabase FUNCTION CALLED ===');
  console.log('vehicleData received:', vehicleData);
  try {
    console.log('addVehicleToSupabase called with data:', vehicleData);

    // Since only GTINV table exists, use it directly
    console.log('Using GTINV table directly (no vehicles table exists)');
    return addVehicleToGTINV(vehicleData);
  } catch (error) {
    console.error('Error in addVehicleToSupabase:', error);
    throw error;
  }
};

/**
 * Fallback function to add a vehicle to the GTINV table
 * @param {Object} vehicleData - The vehicle data to add
 * @returns {Promise} Promise that resolves with the new vehicle ID
 */
const addVehicleToGTINV = async (vehicleData) => {
  try {
    // Extract all fields that exist in the GTINV table
    const {
      title, year, make, model, trim, price,
      image, mileage, bodyStyle, engine, drivetrain,
      exteriorColor, interiorColor
    } = vehicleData;

    // Extract image path from the image URL if it exists
    let image_path = null;
    if (image) {
      if (image.includes('supabase.co/storage/v1/object/public/car-images/')) {
        const match = image.match(/car-images\/(.+)$/);
        if (match && match[1]) {
          image_path = match[1];
        }
      } else if (!image.startsWith('http')) {
        image_path = image;
      }
    }

    // Since RLS is disabled, we'll use a default tenant_id or try to get it from user metadata
    console.log('Getting tenant_id for GTINV table...');
    let tenant_id = 'b316264d-e086-42c8-b50f-1ce5134514ee'; // Default tenant_id from the data

    try {
      const { data: { user }, error: userError } = await supabase.auth.getUser();
      if (!userError && user?.app_metadata?.tenant_id) {
        tenant_id = user.app_metadata.tenant_id;
        console.log('Using tenant_id from user metadata:', tenant_id);
      } else {
        console.log('Using default tenant_id (RLS disabled):', tenant_id);
      }
    } catch (authError) {
      console.log('Auth error, using default tenant_id:', authError.message);
    }

    // Create the vehicle object with correct column names
    const gtinvVehicle = {
      title: title || `${year} ${make} ${model} ${trim || ''}`.trim(),
      year: year || new Date().getFullYear(),
      make: make || '',
      model: model || '',
      trim: trim || '',
      price: price || 0,
      image_path,
      mileage: mileage || 0,
      bodyStyle: bodyStyle || '',
      engine: engine || '',
      driveTrain: drivetrain || '',
      exteriorColor: exteriorColor || '',
      interiorColor: interiorColor || '',
      tenant_id: tenant_id // Add tenant_id for RLS
    };

    console.log('Inserting vehicle to GTINV:', gtinvVehicle);

    const { data, error } = await supabase
      .from('GTINV')
      .insert([gtinvVehicle])
      .select();

    console.log('GTINV insert result:', { data, error });

    if (error) {
      console.error('Error adding vehicle to GTINV table:', error);
      throw error;
    }

    if (!data || data.length === 0) {
      console.error('No data returned from GTINV insert');
      throw new Error('Failed to add vehicle to GTINV: No data returned from insert');
    }

    const vehicleId = data[0].id;
    console.log('Extracted vehicle ID:', vehicleId);

    if (!vehicleId) {
      console.error('No ID found in returned data:', data[0]);
      throw new Error('Failed to add vehicle to GTINV: No ID returned');
    }

    console.log('Successfully added vehicle to GTINV with ID:', vehicleId);
    return vehicleId;
  } catch (error) {
    console.error('Error in addVehicleToGTINV:', error);
    throw error;
  }
};

/**
 * Update a vehicle in the database
 * @param {number} id - The vehicle ID
 * @param {Object} vehicleData - The updated vehicle data
 * @returns {Promise} Promise that resolves when the update is complete
 */
export const updateVehicleInSupabase = async (id, vehicleData) => {
  try {
    console.log('Updating vehicle in GTINV table');
    return updateVehicleInGTINV(id, vehicleData);
  } catch (error) {
    console.error(`Error in updateVehicleInSupabase:`, error);
    throw error;
  }
};

/**
 * Fallback function to update a vehicle in the GTINV table
 * @param {number} id - The vehicle ID
 * @param {Object} vehicleData - The updated vehicle data
 * @returns {Promise} Promise that resolves when the update is complete
 */
const updateVehicleInGTINV = async (id, vehicleData) => {
  try {
    // Extract all fields that exist in the GTINV table
    const {
      year, make, model, trim, price, specialPrice,
      bodyStyle, engine, drivetrain, exteriorColor, interiorColor, mileage,
      doors, passengers, engineSize, transmission, fuelType, cityFuel, hwyFuel,
      stockNumber, vin, highlights, features, description,
      title
    } = vehicleData;
    
    // Create an update object with all fields that exist in the GTINV table
    const updateData = {};
    
    if (title !== undefined) updateData.title = title;
    if (year !== undefined) updateData.year = year;
    if (make !== undefined) updateData.make = make;
    if (model !== undefined) updateData.model = model;
    if (trim !== undefined) updateData.trim = trim;
    if (price !== undefined) updateData.price = price;
    if (specialPrice !== undefined) updateData.specialPrice = specialPrice;
    if (bodyStyle !== undefined) updateData.bodyStyle = bodyStyle;
    if (engine !== undefined) updateData.engine = engine;
    if (drivetrain !== undefined) updateData.driveTrain = drivetrain;
    if (exteriorColor !== undefined) updateData.exteriorColor = exteriorColor;
    if (interiorColor !== undefined) updateData.interiorColor = interiorColor;
    if (mileage !== undefined) updateData.mileage = mileage;
    if (doors !== undefined) updateData.doors = doors;
    if (passengers !== undefined) updateData.passengers = passengers;
    if (engineSize !== undefined) updateData.engineSize = engineSize;
    if (transmission !== undefined) updateData.transmission = transmission;
    if (fuelType !== undefined) updateData.fuelType = fuelType;
    if (cityFuel !== undefined) updateData.cityFuelEconomy = cityFuel;
    if (hwyFuel !== undefined) updateData.highwayFuelEconomy = hwyFuel;
    if (stockNumber !== undefined) updateData.stockNumber = stockNumber;
    if (vin !== undefined) updateData.vin = vin;
    if (highlights !== undefined) updateData.highlights = highlights;
    if (features !== undefined) updateData.features = features;
    if (description !== undefined) updateData.description = description;
    
    // No need to handle images as there's no image column in the database
    
    console.log(`Updating vehicle with ID ${id} in GTINV table with data:`, updateData);
    
    const { error } = await supabase
      .from('GTINV')
      .update(updateData)
      .eq('id', id);
    
    if (error) {
      console.error(`Error updating vehicle with ID ${id} in GTINV table:`, error);
      throw error;
    }
    
    return id;
  } catch (error) {
    console.error(`Error in updateVehicleInGTINV:`, error);
    throw error;
  }
};

/**
 * Delete a vehicle from the database
 * @param {number} id - The vehicle ID
 * @returns {Promise} Promise that resolves when the delete is complete
 */
export const deleteVehicleFromSupabase = async (id) => {
  try {
    console.log('Deleting vehicle from GTINV table');
    return deleteVehicleFromGTINV(id);
  } catch (error) {
    console.error(`Error in deleteVehicleFromSupabase:`, error);
    throw error;
  }
};

/**
 * Fallback function to delete a vehicle from the GTINV table
 * @param {number} id - The vehicle ID
 * @returns {Promise} Promise that resolves when the delete is complete
 */
const deleteVehicleFromGTINV = async (id) => {
  try {
    const { error } = await supabase
      .from('GTINV')
      .delete()
      .eq('id', id);
    
    if (error) {
      console.error(`Error deleting vehicle with ID ${id} from GTINV table:`, error);
      throw error;
    }
    
    return true;
  } catch (error) {
    console.error(`Error in deleteVehicleFromGTINV:`, error);
    throw error;
  }
};

/**
 * Initialize the Supabase database
 * This function checks if the necessary tables exist and creates them if they don't
 * @returns {Promise} Promise that resolves when the database is initialized
 */
export const initSupabaseDatabase = async () => {
  try {
    console.log('Checking GTINV table...');

    const { error: gtinvError } = await supabase
      .from('GTINV')
      .select('id')
      .limit(1);

    if (gtinvError && gtinvError.code === '42P01') {
      console.log('GTINV table not found, creating it');

      // Create the GTINV table
      const { error: createError } = await supabase.rpc('create_gtinv_table');

      if (createError) {
        console.error('Error creating GTINV table:', createError);
        console.log('Table creation via RPC failed, this is expected in development. Tables should be created manually in the Supabase dashboard.');
      }
    } else {
      console.log('GTINV table exists');
    }

    return true;
  } catch (error) {
    console.error('Error initializing Supabase database:', error);
    return false;
  }
};

export default {
  getAllVehiclesFromSupabase,
  getVehicleByIdFromSupabase,
  addVehicleToSupabase,
  updateVehicleInSupabase,
  deleteVehicleFromSupabase,
  initSupabaseDatabase
};
